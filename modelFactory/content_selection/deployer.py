import logging
from datetime import datetime

import pandas as pd

from abstract_model_factory.abstract_deployer import AbstractDeployer
from content_selection.constants import Constants
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer

from sqlalchemy import create_engine

import requests
import os
import time

class Deployer(AbstractDeployer):

    def execute(self):
        print("Executing Deployer")
        if Constants.LEARNING_MODE:
            self.write_model()
        if Constants.EXECUTION_MODE:
            self.write_predictions()

    def qa_module(self):
        pass

    # Save results to S3
    def write_predictions(self):
        data = Data.get_instance()
        prediction_results = data.get_dataframe("prediction_results")

        runmonth = data.get_param("runmonth")
        data_access_layer = DataAccessLayer()
        predictions_s3_dir = Constants.TARGET_S3_PATH + f"runmonth={runmonth}/"
        target_name = "content_selection.parquet"
        data_access_layer.write_predictions_to_s3(prediction_results, predictions_s3_dir, target_name)

    
    def write_model(self):
        data = Data.get_instance()
        model = data.get_model("xgb_model")
        # Use the helper function for consistent path generation
        model_s3_path = DataAccessLayer.get_xgb_model_s3_path()
        DataAccessLayer.write_xgb_model_to_s3(model, model_s3_path)


