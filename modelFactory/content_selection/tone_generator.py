import json
import pandas as pd
import openai
import os
from io import String<PERSON>
from typing import Optional

# Import existing data access layer
from content_selection.data_access_layer import DataAccessLayer
from content_selection.constants import Constants
from aktana_ml_utils import aktana_ml_utils
import sys


def call_openai_api(prompt: str, csv_data: str) -> str:
    try:
        # Combine prompt with CSV data
        full_prompt = f"{prompt}\n\nCSV Data:\n{csv_data}"

        # Make API call
        response = openai.ChatCompletion.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "You are a healthcare marketing language analyst."},
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.1  # Low temperature for consistent results
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        raise e

def initialize_globals() -> str:
    short_opt = "hc:e:a:r:ec:o:l:d"
    long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug=",
                "scenario=", "loglevel=", "autocreate=", "runMode=", "postProcMode=", "journey=",
                "useSCC=", "rundate=", "startdate=", "enddate=", "user=", "incrementalRun="]
    cmd_argv = sys.argv

    ak_ml_utils = aktana_ml_utils()
    cmdline_params, metadata_params = ak_ml_utils.initialize(cmd_argv, short_opt, long_opt, Constants.TEST_MODE)
    params = ak_ml_utils.get_params_json()
    params = json.loads(params)
    
    Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
    Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')
    Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
    Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'content_selection/'

    Constants.rds_hostname = params.get('rds-server')
    Constants.rds_port = params.get('rds-port') if params.get('rds-port') else 3306
    Constants.rds_username = params.get('rds-user')
    Constants.rds_password = params.get('rds-password')
    Constants.engine_db = params.get('rds-enginedbname')
    Constants.stage_db = params.get('rds-stagedbname')
    Constants.learning_db = params.get('rds-learningdbname')

def prepare_tone_data() -> bool:
    
    try:
        initialize_globals()
        print("Step 1: Fetching document data from RDS...")

        # Fetch data from RDS using existing method
        sql_query = """
        SELECT
            cmsDocumentId,
            fragmentBundleId,
            MAX(CASE WHEN fragmentType = 'subject_line' THEN fragmentValue END) AS subject_line,
            MAX(CASE WHEN fragmentType = 'pre_header' THEN fragmentValue END) AS pre_header
        FROM
        AKT_Content_Fragments
        GROUP BY
            cmsDocumentId,
            fragmentBundleId
        """
        df = DataAccessLayer.read_rds_table_as_pandas(sql_query, Constants.stage_db)

        if df.empty:
            print("WARNING: No data returned from RDS query.")
            return False

        print(f"Successfully fetched {len(df)} rows from RDS.")

        # Step 2: Convert dataframe to CSV string
        print("Step 2: Converting dataframe to CSV string...")
        csv_string = df.to_csv(index=False)

        # Step 3: Call OpenAI API
        print("Step 3: Calling OpenAI API to generate tone data...")
        openai_prompt = '''
                You are a healthcare marketing language analyst.

                Your task is to analyze each row in a CSV containing promotional email content from a pharmaceutical company. These emails are intended for doctors who are not currently prescribing the product, and each row contains the following fields:

                - subject_line
                - pre_header

                Based on these fields, assign a **single tone label** for the email's messaging strategy.

                Your goal is to determine the **primary emotional or functional tone** conveyed in the email, which may influence whether a doctor opens it.

                Add a new column called: `message_tone`

                Choose **only one** of the following three values per row:

                ---

                ### Possible `message_tone` values:

                - **Informational** → Updates about product access, tools, or features.

                - **Supportive** → Offers of assistance, follow-ups, meeting outreach, emotional support and motivation.

                - **Action-Oriented** → The message urges the doctor to act now, often using imperative language.

                ---

                **Output Format**:
                Return a CSV table with the original columns **plus** a new column named `message_tone`. Keep all original formatting and column order.
                Return CSV only, no other commentary.

                '''
        response_content = call_openai_api(openai_prompt, csv_string)

        # Step 4: Parse OpenAI response and create final dataframe
        print("Step 4: Parsing OpenAI response...")

        # Assume OpenAI returns CSV format - parse it back to dataframe
        try:
            response_df = pd.read_csv(StringIO(response_content))
            print(f"Successfully parsed OpenAI response into dataframe with {len(response_df)} rows.")
        except Exception as e:
            print(f"Error parsing OpenAI response as CSV: {str(e)}")
            print("OpenAI Response:")
            print(response_content)
            return False

        # Step 5: Write final result to S3 as CSV file
        s3_path = Constants.TARGET_S3_PATH + 'tone_data.csv'
        print(f"Step 5: Writing final result to S3 path '{s3_path}'...")

        # Parse S3 path to extract directory and filename
        s3_parts = s3_path.rstrip('/').split('/')
        target_name = s3_parts[-1]
        s3_dir_path = '/'.join(s3_parts[:-1]) + '/'

        try:
            DataAccessLayer.write_predictions_to_s3(response_df, s3_dir_path, target_name)
            write_success = True
        except Exception as e:
            print(f"❌ Failed to write data to S3: {str(e)}")
            write_success = False

        if not write_success:
            return False

        print(f"✅ Successfully saved tone data to S3 path '{s3_path}'")
        print(f"Final dataset contains {len(response_df)} rows and {len(response_df.columns)} columns.")

        return True

    except Exception as e:
        print(f"❌ Error in prepare_tone_data: {str(e)}")
        return False


if __name__ == "__main__":
    
    success = prepare_tone_data()

    if success:
        print("Tone data preparation completed successfully!")
    else:
        print("Tone data preparation failed!")