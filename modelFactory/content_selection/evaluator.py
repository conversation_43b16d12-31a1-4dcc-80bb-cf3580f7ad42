from datetime import datetime
from datetime import date
import tempfile
import os

import numpy as np
import pandas as pd
from sklearn.metrics import f1_score, precision_recall_curve, roc_auc_score
from xgboost import XGBClassifier

from abstract_model_factory.abstract_evaluator import AbstractEvaluator
from content_selection.constants import Constants
from content_selection.data import Data
from content_selection.data_access_layer import DataAccessLayer
from content_selection.qa_data_handler import QADataHandler

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("SHAP not available. Install with: pip install shap")


class Evaluator(AbstractEvaluator):

    def analyze_feature_quality_with_shap(self, model, X_sample, feature_names, sample_size=1000):
        """
        Enhanced SHAP analysis providing comprehensive feature insights
        """
        if not SHAP_AVAILABLE:
            return {
                "error": "SHAP not available",
                "total_features_analyzed": len(feature_names),
                "extreme_features_count": 0,
                "extreme_features": [],
                "recommendations": ["Install SHAP for feature analysis"]
            }

        try:
            # Sample data for analysis speed
            if len(X_sample) > sample_size:
                sample_idx = np.random.choice(len(X_sample), sample_size, replace=False)
                X_analysis = X_sample.iloc[sample_idx] if hasattr(X_sample, 'iloc') else X_sample[sample_idx]
            else:
                X_analysis = X_sample

            # Save model in JSON format for SHAP compatibility
            # Create temporary JSON file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                temp_json_path = f.name

            try:
                # Save model in JSON format
                model.save_model(temp_json_path)

                # Create new model instance and load from JSON
                shap_model = XGBClassifier(enable_categorical=True)
                shap_model.load_model(temp_json_path)

                # Create SHAP explainer
                explainer = shap.TreeExplainer(shap_model)
                shap_values = explainer.shap_values(X_analysis)

                print("✅ SHAP analysis completed successfully")

            finally:
                # Clean up temporary file
                if os.path.exists(temp_json_path):
                    os.unlink(temp_json_path)

            # Comprehensive feature analysis
            analysis_results = self._perform_comprehensive_shap_analysis(
                shap_values, feature_names, model.feature_importances_, X_analysis
            )

            analysis_results['sample_size_used'] = len(X_analysis)
            return analysis_results

        except Exception as e:
            error_msg = str(e)
            print(f"❌ SHAP analysis failed: {error_msg}")

            # Return consistent structure even on error
            return {
                "error": f"SHAP analysis failed: {error_msg}",
                "total_features_analyzed": len(feature_names),
                "extreme_features_count": 0,
                "extreme_features": [],
                "recommendations": [
                    "SHAP analysis failed - using XGBoost built-in feature importance instead",
                    "Check model compatibility with SHAP version",
                    "Ensure categorical features are properly encoded"
                ]
            }

    def _perform_comprehensive_shap_analysis(self, shap_values, feature_names, model_importance, X_analysis=None):
        """
        Perform comprehensive SHAP analysis with multiple perspectives
        """
        # Basic SHAP statistics
        shap_importance = np.abs(shap_values).mean(0)
        shap_std = np.abs(shap_values).std(0)

        # Feature impact categories
        feature_analysis = []
        extreme_features = []
        inconsistent_features = []

        # Dynamic thresholds based on data distribution
        shap_95th = np.percentile(shap_importance, 95)
        shap_75th = np.percentile(shap_importance, 75)
        extreme_threshold = max(2.0, shap_95th * 0.8)  # Adaptive threshold

        for i, feature in enumerate(feature_names):
            shap_mean = shap_importance[i]
            shap_variance = shap_std[i]
            model_imp = model_importance[i]
            max_shap = np.abs(shap_values[:, i]).max()
            min_shap = np.abs(shap_values[:, i]).min()

            # Feature stability (coefficient of variation)
            cv = shap_variance / (shap_mean + 1e-8)

            # Consistency between SHAP and model importance
            importance_ratio = shap_mean / (model_imp + 1e-8)

            feature_info = {
                'feature': feature,
                'shap_importance': round(shap_mean, 4),
                'shap_std': round(shap_variance, 4),
                'model_importance': round(model_imp, 4),
                'max_shap': round(max_shap, 4),
                'min_shap': round(min_shap, 4),
                'coefficient_variation': round(cv, 3),
                'importance_ratio': round(importance_ratio, 3),
                'stability': 'stable' if cv < 1.0 else 'unstable'
            }

            feature_analysis.append(feature_info)

            # Identify extreme features
            if max_shap > extreme_threshold:
                extreme_features.append(feature_info)

            # Identify inconsistent features (SHAP vs model importance mismatch)
            if importance_ratio > 3.0 or importance_ratio < 0.3:
                inconsistent_features.append(feature_info)

        # Sort features by SHAP importance
        feature_analysis.sort(key=lambda x: x['shap_importance'], reverse=True)

        # Top and bottom features
        top_features = feature_analysis[:10]
        bottom_features = [f for f in feature_analysis if f['shap_importance'] > 0][-5:]

        # Feature interaction insights
        interaction_insights = self._analyze_feature_interactions(shap_values, feature_names)

        # Generate actionable recommendations
        recommendations = self._generate_shap_recommendations(
            extreme_features, inconsistent_features, feature_analysis, interaction_insights
        )

        return {
            'total_features_analyzed': len(feature_names),
            'extreme_features_count': len(extreme_features),
            'extreme_features': extreme_features,
            'inconsistent_features_count': len(inconsistent_features),
            'inconsistent_features': inconsistent_features,
            'top_features': top_features,
            'bottom_features': bottom_features,
            'feature_stability_summary': {
                'stable_features': len([f for f in feature_analysis if f['stability'] == 'stable']),
                'unstable_features': len([f for f in feature_analysis if f['stability'] == 'unstable']),
                'avg_coefficient_variation': round(np.mean([f['coefficient_variation'] for f in feature_analysis]), 3)
            },
            'interaction_insights': interaction_insights,
            'recommendations': recommendations,
            'thresholds_used': {
                'extreme_threshold': round(extreme_threshold, 3),
                'shap_95th_percentile': round(shap_95th, 3),
                'shap_75th_percentile': round(shap_75th, 3)
            }
        }

    def _analyze_feature_interactions(self, shap_values, feature_names, max_interactions=5):
        """
        Analyze feature interactions using SHAP values correlation
        """
        try:
            # Calculate correlation between SHAP values of different features
            shap_df = pd.DataFrame(shap_values, columns=feature_names)
            correlation_matrix = shap_df.corr().abs()

            # Find strong interactions (high correlation between SHAP values)
            interactions = []
            for i in range(len(feature_names)):
                for j in range(i+1, len(feature_names)):
                    corr = correlation_matrix.iloc[i, j]
                    if corr > 0.3:  # Threshold for meaningful interaction
                        interactions.append({
                            'feature_1': feature_names[i],
                            'feature_2': feature_names[j],
                            'shap_correlation': round(corr, 3),
                            'interaction_strength': 'strong' if corr > 0.6 else 'moderate'
                        })

            # Sort by correlation strength
            interactions.sort(key=lambda x: x['shap_correlation'], reverse=True)

            return {
                'total_interactions_found': len(interactions),
                'strong_interactions': len([i for i in interactions if i['interaction_strength'] == 'strong']),
                'top_interactions': interactions[:max_interactions],
                'interaction_summary': f"Found {len(interactions)} feature interactions above 0.3 correlation threshold"
            }

        except Exception as e:
            return {
                'total_interactions_found': 0,
                'strong_interactions': 0,
                'top_interactions': [],
                'interaction_summary': f"Interaction analysis failed: {str(e)}"
            }

    def _generate_shap_recommendations(self, extreme_features, inconsistent_features, feature_analysis, interaction_insights):
        """
        Generate actionable recommendations based on SHAP analysis
        """
        recommendations = []

        # Extreme features recommendations
        if len(extreme_features) > 0:
            recommendations.append(f"🚨 EXTREME IMPACT: {len(extreme_features)} features show unusually high SHAP values")
            recommendations.append("   → Check for data leakage or outliers in these features")
            recommendations.append("   → Consider feature scaling or outlier treatment")

            # Highlight top extreme feature
            top_extreme = max(extreme_features, key=lambda x: x['max_shap'])
            recommendations.append(f"   → Most extreme: '{top_extreme['feature']}' (max SHAP: {top_extreme['max_shap']})")

        # Inconsistent features recommendations
        if len(inconsistent_features) > 0:
            recommendations.append(f"⚠️  INCONSISTENCY: {len(inconsistent_features)} features show SHAP/model importance mismatch")
            recommendations.append("   → These features may have complex non-linear effects")
            recommendations.append("   → Consider feature engineering or interaction terms")

        # Feature stability recommendations
        unstable_count = len([f for f in feature_analysis if f['stability'] == 'unstable'])
        if unstable_count > len(feature_analysis) * 0.3:
            recommendations.append(f"📊 STABILITY: {unstable_count} features show high variance in SHAP values")
            recommendations.append("   → High variance may indicate overfitting or data quality issues")
            recommendations.append("   → Consider regularization or feature selection")

        # Interaction recommendations
        if interaction_insights['strong_interactions'] > 0:
            recommendations.append(f"🔗 INTERACTIONS: {interaction_insights['strong_interactions']} strong feature interactions detected")
            recommendations.append("   → Consider creating interaction features for better model performance")
            recommendations.append("   → Review correlated features for potential redundancy")

        # Top feature insights
        if len(feature_analysis) > 0:
            top_feature = feature_analysis[0]
            recommendations.append(f"⭐ TOP FEATURE: '{top_feature['feature']}' (SHAP importance: {top_feature['shap_importance']})")

            if top_feature['stability'] == 'unstable':
                recommendations.append("   → Top feature is unstable - investigate data quality")

        # General recommendations based on overall analysis
        total_features = len(feature_analysis)
        high_importance_features = len([f for f in feature_analysis if f['shap_importance'] > 0.1])

        if high_importance_features < total_features * 0.1:
            recommendations.append("📈 FEATURE UTILIZATION: Very few features have high importance")
            recommendations.append("   → Consider feature engineering or model complexity adjustment")
        elif high_importance_features > total_features * 0.5:
            recommendations.append("📉 FEATURE UTILIZATION: Many features have high importance")
            recommendations.append("   → Model may be overfitting - consider feature selection")

        # Default recommendation if no issues found
        if not recommendations:
            recommendations.append("✅ SHAP analysis shows healthy feature behavior")
            recommendations.append("   → No major concerns detected in feature impacts")

        return recommendations

    def evaluate_model_performance(self, model, X_test, y_test, X_train, y_train):
        """
        Streamlined evaluation focusing on training-critical metrics

        Args:
            model: Trained XGBoost model
            X_test: Test features
            y_test: Test targets
            X_train: Training features (for overfitting check)
            y_train: Training targets (for overfitting check)

        Returns:
            dict: Essential evaluation metrics
        """
        # Get predictions
        y_test_pred_proba = model.predict_proba(X_test)[:, 1]
        y_train_pred_proba = model.predict_proba(X_train)[:, 1]

        # Core metrics for training monitoring
        test_auc = roc_auc_score(y_test, y_test_pred_proba)
        train_auc = roc_auc_score(y_train, y_train_pred_proba)
        auc_gap = train_auc - test_auc

        # F1 for balanced performance assessment
        y_test_pred_binary = model.predict(X_test)
        test_f1 = f1_score(y_test, y_test_pred_binary)

        evaluation_results = {
            'test_auc': round(test_auc, 4),
            'train_auc': round(train_auc, 4),
            'auc_gap': round(auc_gap, 4),
            'test_f1': round(test_f1, 4),
            'overfitting_warning': auc_gap > 0.05
        }

        return evaluation_results

    def get_training_recommendations(self, evaluation_results, shap_analysis=None):
        """
        Simplified training recommendations based on performance
        """
        recommendations = []
        test_auc = evaluation_results['test_auc']
        train_auc = evaluation_results['train_auc']
        auc_gap = evaluation_results['auc_gap']

        # Primary issue analysis
        if train_auc < 0.7 or test_auc < 0.65:
            recommendations.extend([
                "PRIMARY ISSUE: Underfitting detected (low training AUC)",
                "- Reduce regularization: try reg_alpha=0.1, reg_lambda=0.1",
                "- Increase model complexity: max_depth=6-8, n_estimators=800+",
                "- Higher learning rate: 0.1-0.15",
                "- Check data quality and feature engineering"
            ])

        # Enhanced SHAP-based recommendations
        if shap_analysis and 'error' not in shap_analysis:
            # Add SHAP-specific recommendations
            shap_recommendations = shap_analysis.get('recommendations', [])
            recommendations.extend(shap_recommendations)

        # Overfitting handling
        if auc_gap > 0.10:
            recommendations.extend([
                "SEVERE overfitting detected",
                "- Increase regularization parameters",
                "- Reduce model complexity",
                "- Check for data leakage"
            ])

        # F1 score issues
        if evaluation_results['test_f1'] < 0.3:
            recommendations.append("Low F1: Consider threshold tuning or class rebalancing")

        if not recommendations:
            recommendations.append("Model performance is acceptable")

        return recommendations

    def qa_module(self):
        pass

    def execute(self):
        print("Executing Content Selection Evaluator")

        # Get data instance and retrieve stored results from learner
        data = Data.get_instance()

        # Check if we have a trained model and evaluation data
        if data.get_model("xgb_model") is None:
            print("❌ No trained model found. Please run the learner first.")
            return

        # Retrieve model and evaluation data from learner
        xgb_clf_model = data.get_model("xgb_model")
        X_test = data.get_dataframe("X_test")
        y_test = data.get_param("y_test")
        X_train = data.get_dataframe("X_train")
        y_train = data.get_param("y_train")
        feature_names = data.get_param("feature_names")

        if any(param is None for param in [X_test, y_test, X_train, y_train, feature_names]):
            print("❌ Missing evaluation data. Please ensure learner stores all required data.")
            return

        print("✅ Retrieved trained model and evaluation data")

        # === PHASE 1: MODEL EVALUATION ===
        print("\nPerforming model evaluation...")
        evaluation_results = self.evaluate_model_performance(
            xgb_clf_model, X_test, y_test, X_train, y_train
        )

        # === PHASE 2: SHAP ANALYSIS ===
        shap_analysis = None
        if Constants.ENABLE_SHAP_ANALYSIS:
            print("\nPerforming SHAP analysis...")
            shap_analysis = self.analyze_feature_quality_with_shap(xgb_clf_model, X_test, feature_names)
        else:
            print("\nSHAP analysis disabled by feature flag")

        # === PHASE 3: GENERATE RECOMMENDATIONS ===
        print("\nGenerating training recommendations...")
        recommendations = self.get_training_recommendations(evaluation_results, shap_analysis)

        # === PHASE 4: STORE RESULTS ===
        data.set_param("evaluation_results", evaluation_results)
        if Constants.ENABLE_SHAP_ANALYSIS:
            data.set_param("shap_analysis", shap_analysis)
        data.set_param("training_recommendations", recommendations)

        print("✅ Evaluation completed and results stored")

        # === PHASE 5: DISPLAY RESULTS ===
        self._display_evaluation_results(evaluation_results, shap_analysis, recommendations)

    def _display_evaluation_results(self, evaluation_results, shap_analysis, recommendations):
        """Display comprehensive evaluation results"""
        # Print basic results
        print(f"\n{'='*50}")
        print("EVALUATION RESULTS")
        print(f"{'='*50}")
        print(f"Test AUC: {evaluation_results['test_auc']}")
        print(f"Train AUC: {evaluation_results['train_auc']}")
        print(f"AUC Gap: {evaluation_results['auc_gap']}")
        print(f"Test F1: {evaluation_results['test_f1']}")

        if evaluation_results['overfitting_warning']:
            print("⚠️  WARNING: Potential overfitting detected")

        # Enhanced SHAP results
        if Constants.ENABLE_SHAP_ANALYSIS and shap_analysis and 'error' not in shap_analysis:
            print(f"\n{'='*50}")
            print("COMPREHENSIVE SHAP ANALYSIS")
            print(f"{'='*50}")

            # Overview
            print(f"📊 Features analyzed: {shap_analysis['total_features_analyzed']}")
            print(f"📈 Sample size used: {shap_analysis['sample_size_used']}")

            # Feature stability summary
            stability = shap_analysis['feature_stability_summary']
            print(f"\n🔍 Feature Stability:")
            print(f"   Stable features: {stability['stable_features']}")
            print(f"   Unstable features: {stability['unstable_features']}")
            print(f"   Avg coefficient of variation: {stability['avg_coefficient_variation']}")

            # Top features
            print(f"\n⭐ Top 5 Most Important Features (by SHAP):")
            for i, feat in enumerate(shap_analysis['top_features'][:5], 1):
                stability_icon = "🟢" if feat['stability'] == 'stable' else "🔴"
                print(f"   {i}. {feat['feature']} {stability_icon}")
                print(f"      SHAP: {feat['shap_importance']}, Model: {feat['model_importance']}")

            # Extreme features
            if shap_analysis['extreme_features_count'] > 0:
                print(f"\n🚨 Extreme Impact Features ({shap_analysis['extreme_features_count']}):")
                for feat in shap_analysis['extreme_features'][:3]:
                    print(f"   - {feat['feature']}: max_shap={feat['max_shap']}, CV={feat['coefficient_variation']}")

            # Inconsistent features
            if shap_analysis['inconsistent_features_count'] > 0:
                print(f"\n⚠️  Inconsistent Features ({shap_analysis['inconsistent_features_count']}):")
                for feat in shap_analysis['inconsistent_features'][:3]:
                    print(f"   - {feat['feature']}: ratio={feat['importance_ratio']}")

            # Feature interactions
            interactions = shap_analysis['interaction_insights']
            if interactions['total_interactions_found'] > 0:
                print(f"\n🔗 Feature Interactions:")
                print(f"   Total interactions: {interactions['total_interactions_found']}")
                print(f"   Strong interactions: {interactions['strong_interactions']}")
                if interactions['top_interactions']:
                    print("   Top interactions:")
                    for inter in interactions['top_interactions'][:3]:
                        print(f"     {inter['feature_1']} ↔ {inter['feature_2']} (corr: {inter['shap_correlation']})")

            # Thresholds used
            thresholds = shap_analysis['thresholds_used']
            print(f"\n📏 Analysis Thresholds:")
            print(f"   Extreme threshold: {thresholds['extreme_threshold']}")
            print(f"   95th percentile: {thresholds['shap_95th_percentile']}")

        elif Constants.ENABLE_SHAP_ANALYSIS and shap_analysis and 'error' in shap_analysis:
            print(f"\n❌ SHAP Analysis Failed: {shap_analysis['error']}")
        elif not Constants.ENABLE_SHAP_ANALYSIS:
            print(f"\n🚫 SHAP Analysis Disabled: Feature flag ENABLE_SHAP_ANALYSIS is set to False")

        # Comprehensive Recommendations
        print(f"\n{'='*50}")
        print("ACTIONABLE RECOMMENDATIONS")
        print(f"{'='*50}")

        if Constants.ENABLE_SHAP_ANALYSIS:
            # Separate SHAP recommendations from general ones
            shap_recs = []
            general_recs = []

            for rec in recommendations:
                if any(indicator in rec for indicator in ['🚨', '⚠️', '📊', '🔗', '⭐', '📈', '📉', '✅']):
                    shap_recs.append(rec)
                else:
                    general_recs.append(rec)

            # Display SHAP recommendations first
            if shap_recs:
                print("🔍 SHAP-Based Insights:")
                for rec in shap_recs:
                    print(f"   {rec}")

            # Display general recommendations
            if general_recs:
                print(f"\n🎯 General Training Recommendations:")
                for i, rec in enumerate(general_recs, 1):
                    print(f"   {i}. {rec}")
        else:
            # Display all recommendations without SHAP categorization
            print("🎯 Training Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")

        print(f"\n{'='*50}")
